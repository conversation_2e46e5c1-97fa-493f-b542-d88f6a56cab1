.PHONY: build run echo db-test cli clean

# 构建应用
build:
	go build -o bin/app ./cmd/app
	go build -o bin/cli ./cmd/cli

# 运行 HTTP 服务
run:
	go run ./cmd/app/main.go

# 运行 CLI 工具
cli:
	go run ./cmd/cli/main.go

# 运行 echo 命令
echo:
	go run ./cmd/cli/main.go echo

# 运行数据库测试
db-test:
	go run ./cmd/cli/main.go db test

# 直接运行 CLI 二进制
cli-bin: build
	./bin/cli

# 直接运行 echo 二进制
echo-bin: build
	./bin/cli echo

# 直接运行 db test 二进制
db-test-bin: build
	./bin/cli db test

# 清理构建产物
clean:
	rm -rf bin/
	rm -rf logs/

# 安装依赖
deps:
	go mod tidy
	go mod download

# 测试
test:
	go test -v ./...

# 格式化代码
fmt:
	go fmt ./...

# 检查代码
lint:
	golangci-lint run

# 创建日志目录
setup:
	mkdir -p logs
	mkdir -p bin
