# 项目规则（Cursor Rules）

## 注意信息

监听端口: 8800
日志地址: ./logs/app.log ./logs/app.log.wf
日志保留: 3 天
正确处理: .ignore 等文件

## 目标

* 本项目是使用 **Go 1.22+**、**CloudWeGo Hertz** 作为 HTTP 框架、**GORM** 作为 ORM 的后端服务。
* 所有 AI 生成/修改代码必须 **可编译**、**有单测示例**（必要处），并遵循本文约定。
* **默认数据库：MySQL 8+**。JSON 返回统一外层包 `{code, message, data}`。

---

## 技术与依赖（只用这些，除非我另说）

* Web：`github.com/cloudwego/hertz`
* ORM：`gorm.io/gorm`，驱动 `gorm.io/driver/mysql`
* 迁移：`github.com/go-gormigrate/gormigrate/v2`
* 配置：`github.com/spf13/viper`
* 校验：`github.com/go-playground/validator/v10`（Hertz 绑定校验优先）
* 日志：`go.uber.org/zap`（通过 `hlog` 适配 Hertz）
* ID/链路追踪：`X-Request-ID` 中间件（若落地 OTel 再补充）

---

## 目录结构（新代码必须遵循）

```
.
├─ cmd/
│  └─ app/main.go
├─ internal/
│  ├─ config/           # 配置加载与结构体
│  ├─ server/           # hertz 初始化、路由注册、middleware
│  ├─ handler/          # HTTP 层（controller）
│  ├─ service/          # 业务服务层（无框架依赖）
│  ├─ repo/             # 仓储层（GORM 访问）
│  ├─ model/            # GORM 模型定义
│  ├─ migrator/         # gormigrate 迁移
│  ├─ middleware/       # Hertz 中间件
│  ├─ pkg/              # 通用组件（响应包装、错误、工具）
│  └─ dto/              # 请求/响应 DTO
├─ scripts/             # 启动、迁移、代码生成脚本
├─ configs/             # 默认配置文件（YAML）
└─ .cursorrules
```

---

## 编码规范（必须遵循）

1. **Context**：service/repo 的所有对外方法签名第一个参数为 `context.Context`。
2. **错误处理**：使用 `%w` 包装；对外返回项目自定义错误码，内部日志保留具体错误。
3. **日志**：统一 `zap`，HTTP 层使用 `hlog`，日志字段包含 `request_id`、`latency`、`method`、`path`、`code`。
4. **JSON & 命名**：数据库列 `snake_case`；API JSON `camelCase`；`time.Time` 使用 `RFC3339` 字符串。
5. **分页**：约定参数 `page`（从1开始）、`pageSize`，最大 `100`，默认 `20`。
6. **事务**：只能在 service 层开启，`db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {...})`，repo 提供 `WithTx(tx)` 能力。
7. **校验**：DTO 使用 `validate` tag；错误返回统一为字段错误数组。
8. **路由**：所有业务路由挂载在 `/api/v1` 下，RESTful 风格。
9. **中间件**：`recover`、`request-id`、`logger`、`gzip`、`cors`（按需）默认开启。
10. **测试**：handler 层用 httptest；service/repo 层使用模拟或临时测试库（sqlite-memory 仅限单测）。
11. **依赖倒置**：handler 只调 service，service 只调 repo。禁止在 handler 里直接操作 GORM。
12. **生成物**：新增资源时必须提供路由、DTO、handler、service、repo、model、迁移、最小单测、`curl` 示例。

---

## 统一响应

```go
type Resp[T any] struct {
    Code    int    `json:"code"`
    Message string `json:"message"`
    Data    T      `json:"data,omitempty"`
}
func Ok[T any](data T) Resp[T]          { return Resp[T]{Code: 0, Message: "ok", Data: data} }
func Err(code int, msg string) Resp[any] { return Resp[any]{Code: code, Message: msg} }
```

---

## 启动样例（必须类似）

```go
// cmd/app/main.go
package main

import (
    "context"
    "github.com/cloudwego/hertz/pkg/app/server"
    "github.com/cloudwego/hertz/pkg/common/hlog"
    "go.uber.org/zap"
    "gorm.io/driver/mysql"
    "gorm.io/gorm"
    "time"

    "myapp/internal/config"
    "myapp/internal/server/router"
)

type Deps struct {
    DB     *gorm.DB
    Logger *zap.Logger
}

func main() {
    // 配置
    cfg := config.Load() // viper 读取 configs/app.yaml, 支持 env 覆盖

    // 日志
    logger, _ := zap.NewProduction()
    hlog.SetLogger(hlog.NewLogger(logger.Sugar()))

    // DB
    db, err := gorm.Open(mysql.Open(cfg.MySQL.DSN), &gorm.Config{})
    if err != nil { panic(err) }
    if sqlDB, _ := db.DB(); sqlDB != nil {
        sqlDB.SetMaxIdleConns(10)
        sqlDB.SetMaxOpenConns(50)
        sqlDB.SetConnMaxLifetime(time.Hour)
    }

    deps := &Deps{DB: db, Logger: logger}

    // Hertz
    h := server.New(server.WithHostPorts(cfg.HTTP.Addr))
    router.Register(h, deps) // 注册中间件与业务路由
    h.Spin()
}
```

---

## 配置示例（YAML）

```yaml
# configs/app.yaml
http:
    addr: ":8080"
mysql:
    dsn: "user:pass@tcp(127.0.0.1:3306)/myapp?parseTime=true&loc=UTC&charset=utf8mb4"
log:
    level: "info"
```

---

## 中间件约定

* `Recover`：拦截 panic 返回 `500`，日志带堆栈。
* `RequestID`：读取 `X-Request-ID`，无则生成并回写。
* `Logger`：请求/响应日志（含状态码、耗时、体积上限）。
* `CORS/Gzip`：按需启用。

路由注册示例：

```go
// internal/server/router/router.go
func Register(h *server.Hertz, d *Deps) {
    useMiddlewares(h, d)
    v1 := h.Group("/api/v1")
    user.RegisterRoutes(v1, d)
}
```

---

## 模型与迁移

* 模型基类：

```go
type BaseModel struct {
    ID        uint64         `gorm:"primaryKey;autoIncrement" json:"id"`
    CreatedAt time.Time      `json:"createdAt"`
    UpdatedAt time.Time      `json:"updatedAt"`
    DeletedAt gorm.DeletedAt `gorm:"index" json:"-"`
}
```

* 使用 `gormigrate` 管理 DDL，不在运行时自动迁移：

```go
m := gormigrate.New(db, gormigrate.DefaultOptions, []*gormigrate.Migration{
    {
        ID: "20250819_create_users",
        Migrate: func(tx *gorm.DB) error { return tx.AutoMigrate(&model.User{}) },
        Rollback: func(tx *gorm.DB) error { return tx.Migrator().DropTable(&model.User{}) },
    },
})
if err := m.Migrate(); err != nil { return err }
```

---

## GORM 约定

* 禁止在 repo 外部使用 `db`。
* 读操作默认 `WithContext(ctx)`；写操作必须在事务内（如涉及多表）。
* 批量操作使用 `CreateInBatches`；分页用 `Limit/Offset` 并返回 `total`。
* 软删默认开启，物理删需注明 `Unscoped().Delete(...)`。
* 乐观锁按需使用 `version` 字段（`gorm:"version"`）。

---

## Hertz 约定

* DTO 使用 `query/form/json` 绑定 + `validate` 标签；错误返回统一字段错误数组：

```go
type CreateUserReq struct {
    Name  string `json:"name" validate:"required,min=2,max=32"`
    Email string `json:"email" validate:"required,email"`
}
```

---

## Handler / Service / Repo 模板

### Model

```go
// internal/model/user.go
type User struct {
    BaseModel
    Name  string `gorm:"type:varchar(64);not null;index:idx_user_email,priority:2" json:"name"`
    Email string `gorm:"type:varchar(128);not null;uniqueIndex:idx_user_email" json:"email"`
}
func (User) TableName() string { return "users" }
```

### Repo

```go
// internal/repo/user_repo.go
type UserRepo interface {
    Create(ctx context.Context, u *model.User) error
    FindByID(ctx context.Context, id uint64) (*model.User, error)
    List(ctx context.Context, page, size int) (items []*model.User, total int64, err error)
    WithTx(tx *gorm.DB) UserRepo
}

type userRepo struct{ db *gorm.DB }

func NewUserRepo(db *gorm.DB) UserRepo { return &userRepo{db: db} }
func (r *userRepo) WithTx(tx *gorm.DB) UserRepo { return &userRepo{db: tx} }
func (r *userRepo) Create(ctx context.Context, u *model.User) error { return r.db.WithContext(ctx).Create(u).Error }
func (r *userRepo) FindByID(ctx context.Context, id uint64) (*model.User, error) {
    var u model.User
    if err := r.db.WithContext(ctx).First(&u, id).Error; err != nil { return nil, err }
    return &u, nil
}
func (r *userRepo) List(ctx context.Context, page, size int) ([]*model.User, int64, error) {
    if size <= 0 { size = 20 }
    if size > 100 { size = 100 }
    if page <= 0 { page = 1 }
    var total int64
    r.db.WithContext(ctx).Model(&model.User{}).Count(&total)
    var list []*model.User
    err := r.db.WithContext(ctx).Order("id desc").Limit(size).Offset((page-1)*size).Find(&list).Error
    return list, total, err
}
```

### Service

```go
// internal/service/user_service.go
type UserService interface {
    CreateUser(ctx context.Context, name, email string) (*model.User, error)
}

type userService struct{ db *gorm.DB; repo repo.UserRepo }

func NewUserService(db *gorm.DB, r repo.UserRepo) UserService { return &userService{db: db, repo: r} }

func (s *userService) CreateUser(ctx context.Context, name, email string) (*model.User, error) {
    var out *model.User
    err := s.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
        u := &model.User{Name: name, Email: email}
        if err := s.repo.WithTx(tx).Create(ctx, u); err != nil { return err }
        out = u
        return nil
    })
    return out, err
}
```

### Handler

```go
// internal/handler/user_handler.go
type UserHandler struct{ svc service.UserService }

func NewUserHandler(s service.UserService) *UserHandler { return &UserHandler{svc: s} }

type createUserReq struct {
    Name  string `json:"name" validate:"required,min=2,max=32"`
    Email string `json:"email" validate:"required,email"`
}

func (h *UserHandler) Create(c context.Context, ctx *app.RequestContext) {
    var req createUserReq
    if err := ctx.BindAndValidate(&req); err != nil {
        ctx.JSON(400, Err(10001, err.Error()))
        return
    }
    u, err := h.svc.CreateUser(c, req.Name, req.Email)
    if err != nil {
        ctx.JSON(500, Err(10000, "internal error"))
        return
    }
    ctx.JSON(200, Ok(u))
}
```

### Router 注册

```go
// internal/server/router/user_route.go
func RegisterRoutes(r *route.RouterGroup, d *Deps) {
    ur := repo.NewUserRepo(d.DB)
    us := service.NewUserService(d.DB, ur)
    uh := handler.NewUserHandler(us)

    users := r.Group("/users")
    users.POST("/", uh.Create)
    // 其他 CRUD...
}
```

---

## 请求校验与错误返回（统一格式）

* 400：`{code: 10001, message: "validation error: {field} {reason}", data: null}`
* 404：`{code: 10004, message: "not found"}`
* 500：`{code: 10000, message: "internal error"}`

字段级错误建议：

```json
{ "code": 10001, "message": "validation failed", "data": [{"field":"email","message":"must be a valid email"}] }
```

---

## 新增“资源”的标准步骤（AI 执行清单）

当我说“**给我加一个 {Resource} 的 CRUD**”时，按此顺序提交代码变更：

1. `internal/model/{resource}.go` 创建模型与索引 tag。
2. `internal/migrator/20xxxxxx_{resource}.go` 增加迁移。
3. `internal/repo/{resource}_repo.go` 定义接口与实现（含分页）。
4. `internal/service/{resource}_service.go` 写业务方法（需要事务的放这里）。
5. `internal/dto/{resource}.go` 写请求/响应 DTO + `validate`。
6. `internal/handler/{resource}_handler.go` 写 handler 并使用 DTO & service。
7. `internal/server/router/{resource}_route.go` 注册路由到 `/api/v1/{resources}`。
8. `cmd/app/main.go` 确认已调用 router。
9. 单测：至少为 service 或 repo 增加最小可运行测试（sqlite in-memory）。
10. 提供 `curl` 示例（Create/Show/List）。

---

## 测试约定（最小可行）

```go
// internal/service/user_service_test.go
func TestCreateUser(t *testing.T) {
    db := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
    _ = db.AutoMigrate(&model.User{})
    r := repo.NewUserRepo(db)
    s := service.NewUserService(db, r)
    u, err := s.CreateUser(context.Background(), "Alice", "<EMAIL>")
    require.NoError(t, err)
    require.NotZero(t, u.ID)
}
```

---

## 安全与性能

* **不要**在响应中回显敏感字段（密码、token）。
* 数据库查询必须设置合理索引；列表接口默认按 `id desc`。
* 对外接口限流按需补充（IP/UID 维度）。
* 长耗时任务下放到异步（仅返回任务ID）。

---

## 提交流程与风格

* 使用 Conventional Commits：`feat: user create api`、`fix: pagination total wrong`。
* 变更附带简短说明与运行方式（若有迁移，标注迁移 ID）。

---

## 常用片段（可直接插入）

**分页工具**

```go
func Paginate(page, size int) (limit, offset int) {
    if size <= 0 { size = 20 }
    if size > 100 { size = 100 }
    if page <= 0 { page = 1 }
    return size, (page-1)*size
}
```

**统一绑定+校验错误处理（Hertz）**

```go
func BindAndValidate(ctx *app.RequestContext, v any) bool {
    if err := ctx.BindAndValidate(v); err != nil {
        ctx.JSON(400, Err(10001, err.Error()))
        return false
    }
    return true
}
```

**事务模板（service 层）**

```go
func InTx(ctx context.Context, db *gorm.DB, fn func(tx *gorm.DB) error) error {
    return db.WithContext(ctx).Transaction(fn)
}
```

---

## 我希望 Cursor 在这些场景自动做的事

* 当新增资源时，自动生成：模型、迁移、repo、service、handler、路由、最小单测、`curl` 示例，并遵循上面的目录与约定。
* 当我提供表结构或字段变更时，自动生成/更新 `gormigrate` 迁移脚本，禁止直接 `AutoMigrate`。
* 当我要求“加入分页/校验/错误码”，补充 DTO tag、分页逻辑、统一响应。
* 所有提交的代码块必须能独立编译或至少在上下文中引用齐全。
