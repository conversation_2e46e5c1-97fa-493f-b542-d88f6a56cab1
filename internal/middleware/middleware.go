package middleware

import (
	"context"
	"crypto/rand"
	"fmt"
	"time"

	"github.com/cloudwego/hertz/pkg/app"
	"github.com/cloudwego/hertz/pkg/common/hlog"
	"github.com/cloudwego/hertz/pkg/protocol/consts"
)

// generateRequestID 生成一个简单的请求 ID
func generateRequestID() string {
	b := make([]byte, 16)
	rand.Read(b)
	return fmt.Sprintf("%x", b)
}

// RequestID 中间件 - 添加请求 ID
func RequestID() app.HandlerFunc {
	return func(c context.Context, ctx *app.RequestContext) {
		requestID := string(ctx.GetHeader("X-Request-ID"))
		if requestID == "" {
			requestID = generateRequestID()
		}
		ctx.Header("X-Request-ID", requestID)
		c = context.WithValue(c, "request_id", requestID)
		ctx.Next(c)
	}
}

// Logger 中间件 - 请求日志
func Logger() app.HandlerFunc {
	return func(c context.Context, ctx *app.RequestContext) {
		start := time.Now()

		requestID := c.Value("request_id")
		if requestID == nil {
			requestID = "unknown"
		}

		ctx.Next(c)

		latency := time.Since(start)
		statusCode := ctx.Response.StatusCode()

		hlog.CtxInfof(c, "[%s] %s %s %d %v",
			requestID,
			string(ctx.Method()),
			string(ctx.Path()),
			statusCode,
			latency,
		)
	}
}

// Recover 中间件 - 恢复 panic
func Recover() app.HandlerFunc {
	return func(c context.Context, ctx *app.RequestContext) {
		defer func() {
			if r := recover(); r != nil {
				requestID := c.Value("request_id")
				if requestID == nil {
					requestID = "unknown"
				}
				hlog.CtxErrorf(c, "[%s] panic recovered: %v", requestID, r)
				ctx.JSON(consts.StatusInternalServerError, map[string]interface{}{
					"code":    10000,
					"message": "internal error",
				})
				ctx.Abort()
			}
		}()
		ctx.Next(c)
	}
}
